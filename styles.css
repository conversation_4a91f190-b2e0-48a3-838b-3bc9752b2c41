/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00ffff;
    --secondary-color: #ff00ff;
    --accent-color: #ffff00;
    --success-color: #00ff00;
    --warning-color: #ff8800;
    --danger-color: #ff0040;
    --dark-bg: #0a0a0a;
    --card-bg: #1a1a2e;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-glow: #00ffff;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-neon: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
}

body {
    font-family: 'Raj<PERSON>ni', sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Enhanced Animated Background System */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    overflow: hidden;
    background: radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 0, 0.02) 0%, transparent 50%);
}

/* Neon Wave System */
.neon-waves-container {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.neon-wave {
    position: absolute;
    width: 200%;
    height: 200px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.1) 25%,
        rgba(0, 255, 255, 0.3) 50%,
        rgba(0, 255, 255, 0.1) 75%,
        transparent 100%
    );
    border-radius: 50%;
    filter: blur(2px);
    animation: waveMove 15s linear infinite;
}

.wave-1 {
    top: 20%;
    left: -50%;
    animation-delay: 0s;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.15) 50%,
        transparent 100%
    );
}

.wave-2 {
    top: 60%;
    left: -50%;
    animation-delay: 5s;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 0, 255, 0.15) 50%,
        transparent 100%
    );
}

.wave-3 {
    top: 40%;
    left: -50%;
    animation-delay: 10s;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 0, 0.1) 50%,
        transparent 100%
    );
}

@keyframes waveMove {
    0% {
        transform: translateX(-100%) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) rotate(360deg);
        opacity: 0;
    }
}

/* Dynamic Geometric Shapes */
.geometric-shapes-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.geometric-shape {
    position: absolute;
    opacity: 0.6;
    animation: geometricFloat 20s ease-in-out infinite;
}

.triangle-1 {
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 50px solid rgba(0, 255, 255, 0.2);
    top: 15%;
    left: 10%;
    animation-delay: 0s;
    filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5));
}

.triangle-2 {
    width: 0;
    height: 0;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-bottom: 40px solid rgba(255, 0, 255, 0.2);
    top: 70%;
    right: 15%;
    animation-delay: 7s;
    filter: drop-shadow(0 0 10px rgba(255, 0, 255, 0.5));
}

.hexagon-1 {
    width: 40px;
    height: 23px;
    background: rgba(255, 255, 0, 0.2);
    position: relative;
    top: 30%;
    right: 20%;
    animation-delay: 3s;
    filter: drop-shadow(0 0 8px rgba(255, 255, 0, 0.5));
}

.hexagon-1::before,
.hexagon-1::after {
    content: "";
    position: absolute;
    width: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
}

.hexagon-1::before {
    bottom: 100%;
    border-bottom: 12px solid rgba(255, 255, 0, 0.2);
}

.hexagon-1::after {
    top: 100%;
    border-top: 12px solid rgba(255, 255, 0, 0.2);
}

.hexagon-2 {
    width: 35px;
    height: 20px;
    background: rgba(0, 255, 255, 0.2);
    position: relative;
    top: 60%;
    left: 80%;
    animation-delay: 10s;
    filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.5));
}

.hexagon-2::before,
.hexagon-2::after {
    content: "";
    position: absolute;
    width: 0;
    border-left: 17.5px solid transparent;
    border-right: 17.5px solid transparent;
}

.hexagon-2::before {
    bottom: 100%;
    border-bottom: 10px solid rgba(0, 255, 255, 0.2);
}

.hexagon-2::after {
    top: 100%;
    border-top: 10px solid rgba(0, 255, 255, 0.2);
}

.diamond-1 {
    width: 30px;
    height: 30px;
    background: rgba(255, 0, 255, 0.2);
    transform: rotate(45deg);
    top: 50%;
    left: 5%;
    animation-delay: 14s;
    filter: drop-shadow(0 0 8px rgba(255, 0, 255, 0.5));
}

@keyframes geometricFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-30px) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-60px) rotate(180deg);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-30px) rotate(270deg);
        opacity: 0.7;
    }
}

/* Enhanced Particle System */
.particles-system {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-particle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.8;
    animation: floatParticle 15s linear infinite;
}

.energy-particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.energy-particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 10px currentColor;
    animation: energyFlow 8s linear infinite;
}

.spark-particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.spark-particle {
    position: absolute;
    width: 2px;
    height: 8px;
    background: linear-gradient(to bottom, var(--accent-color), transparent);
    animation: sparkFall 3s linear infinite;
}

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.8;
    }
    90% {
        opacity: 0.8;
    }
    100% {
        transform: translateY(-100px) translateX(100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes energyFlow {
    0% {
        transform: translateX(-100px) scale(0);
        opacity: 0;
    }
    20% {
        opacity: 1;
        transform: scale(1);
    }
    80% {
        opacity: 1;
    }
    100% {
        transform: translateX(calc(100vw + 100px)) scale(0);
        opacity: 0;
    }
}

@keyframes sparkFall {
    0% {
        transform: translateY(-20px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(180deg);
        opacity: 0;
    }
}

/* Enhanced Glowing Orbs */
.glowing-orbs-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.glowing-orb {
    position: absolute;
    animation: orbFloat 25s ease-in-out infinite;
}

.orb-core {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle, currentColor 0%, transparent 70%);
    filter: blur(2px);
    animation: coreGlow 4s ease-in-out infinite alternate;
}

.orb-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 2px solid currentColor;
    opacity: 0.3;
    animation: ringExpand 6s ease-in-out infinite;
}

.ring-1 {
    width: 80px;
    height: 80px;
    margin: -40px 0 0 -40px;
    animation-delay: 0s;
}

.ring-2 {
    width: 120px;
    height: 120px;
    margin: -60px 0 0 -60px;
    animation-delay: 2s;
}

.orb-1 {
    color: var(--primary-color);
    top: 15%;
    left: 85%;
    animation-delay: 0s;
}

.orb-2 {
    color: var(--secondary-color);
    top: 75%;
    left: 10%;
    animation-delay: 8s;
}

.orb-3 {
    color: var(--accent-color);
    top: 45%;
    left: 70%;
    animation-delay: 16s;
}

@keyframes orbFloat {
    0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.4;
    }
    25% {
        transform: translate(-40px, -20px) scale(1.1);
        opacity: 0.7;
    }
    50% {
        transform: translate(20px, -40px) scale(0.9);
        opacity: 0.5;
    }
    75% {
        transform: translate(-10px, 30px) scale(1.05);
        opacity: 0.6;
    }
}

@keyframes coreGlow {
    0% {
        filter: blur(2px) brightness(1);
        transform: scale(1);
    }
    100% {
        filter: blur(3px) brightness(1.5);
        transform: scale(1.1);
    }
}

@keyframes ringExpand {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.1;
    }
}

/* Enhanced Circuit Pattern */
.circuit-pattern {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.4;
}

.circuit-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(90deg, transparent 49%, rgba(0, 255, 255, 0.1) 50%, transparent 51%),
        linear-gradient(0deg, transparent 49%, rgba(255, 0, 255, 0.1) 50%, transparent 51%);
    background-size: 80px 80px;
    animation: circuitMove 40s linear infinite;
}

.circuit-nodes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.circuit-node {
    position: absolute;
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 10px currentColor;
    animation: nodeGlow 3s ease-in-out infinite alternate;
}

.data-streams {
    position: absolute;
    width: 100%;
    height: 100%;
}

.data-stream {
    position: absolute;
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom, var(--secondary-color), transparent);
    animation: dataFlow 4s linear infinite;
}

@keyframes circuitMove {
    0% {
        background-position: 0 0, 0 0;
    }
    100% {
        background-position: 80px 80px, 80px 80px;
    }
}

@keyframes nodeGlow {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 1;
        transform: scale(1.3);
    }
}

@keyframes dataFlow {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* Interactive Light Rays */
.light-rays-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.light-ray {
    position: absolute;
    width: 2px;
    height: 100vh;
    background: linear-gradient(to bottom,
        transparent 0%,
        var(--primary-color) 50%,
        transparent 100%
    );
    opacity: 0.3;
    animation: lightRayMove 12s linear infinite;
    filter: blur(1px);
}

@keyframes lightRayMove {
    0% {
        transform: translateX(-100px) scaleY(0);
        opacity: 0;
    }
    10% {
        opacity: 0.3;
        transform: scaleY(1);
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateX(100vw) scaleY(0);
        opacity: 0;
    }
}

/* Performance Optimizations */
.animated-background * {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Click Effect Animation */
@keyframes clickExpand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(10);
        opacity: 0;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .animated-background * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
    }
}

/* Enhanced Navigation System */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(135deg,
        rgba(10, 10, 10, 0.95) 0%,
        rgba(26, 26, 46, 0.95) 50%,
        rgba(10, 10, 10, 0.95) 100%
    );
    backdrop-filter: blur(15px);
    z-index: 2000;
    border-bottom: 2px solid transparent;
    border-image: linear-gradient(90deg,
        var(--primary-color),
        var(--secondary-color),
        var(--accent-color)
    ) 1;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 20px rgba(0, 255, 255, 0.1);
}

.navbar.scrolled {
    background: linear-gradient(135deg,
        rgba(5, 5, 5, 0.98) 0%,
        rgba(15, 15, 25, 0.98) 50%,
        rgba(5, 5, 5, 0.98) 100%
    );
    box-shadow: 0 5px 30px rgba(0, 255, 255, 0.2);
    border-bottom-width: 1px;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    transition: height 0.3s ease;
}

.navbar.scrolled .nav-container {
    height: 70px;
}

/* Enhanced Logo */
.nav-logo {
    display: flex;
    align-items: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 0 15px var(--primary-color);
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-logo:hover {
    transform: scale(1.05);
    text-shadow: 0 0 25px var(--primary-color);
}

.logo-icon {
    margin-right: 12px;
    font-size: 2rem;
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-3px) rotate(5deg); }
}

.logo-text {
    position: relative;
    z-index: 2;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    border-radius: 50%;
    filter: blur(10px);
}

.nav-logo:hover .logo-glow {
    opacity: 0.3;
    animation: logoGlowPulse 2s ease-in-out infinite;
}

@keyframes logoGlowPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
}

/* Enhanced Navigation Menu */
.nav-menu {
    display: flex;
    list-style: none;
    gap: 20px;
    align-items: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    padding: 12px 20px;
    border-radius: 25px;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    font-size: 0.95rem;
}

.nav-icon {
    font-size: 1rem;
    transition: all 0.3s ease;
}

.nav-link-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.1),
        rgba(255, 0, 255, 0.1)
    );
    border-radius: 25px;
    transform: scale(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
}

.nav-link:hover {
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 255, 255, 0.3);
}

.nav-link:hover .nav-link-bg {
    transform: scale(1);
}

.nav-link:hover .nav-icon {
    transform: scale(1.2) rotate(5deg);
    color: var(--secondary-color);
}

.nav-link.active {
    color: var(--primary-color);
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.nav-link.active .nav-icon {
    color: var(--secondary-color);
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--dark-bg);
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
}

.theme-toggle {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(0, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary);
}

.theme-toggle:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: rotate(180deg);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

/* Enhanced Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2001;
}

.hamburger:hover {
    background: rgba(0, 255, 255, 0.1);
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 2px;
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
}

.hamburger.active .bar-1 {
    transform: rotate(-45deg) translate(-5px, 6px);
    background: var(--secondary-color);
}

.hamburger.active .bar-2 {
    opacity: 0;
    transform: translateX(20px);
}

.hamburger.active .bar-3 {
    transform: rotate(45deg) translate(-5px, -6px);
    background: var(--secondary-color);
}

/* Navigation Progress Indicator */
.nav-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg,
        var(--primary-color),
        var(--secondary-color),
        var(--accent-color)
    );
    transition: width 0.3s ease;
    width: 0%;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 20px;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: linear-gradient(135deg,
            rgba(10, 10, 10, 0.98) 0%,
            rgba(26, 26, 46, 0.98) 100%
        );
        width: 100%;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(20px);
        border-top: 2px solid var(--primary-color);
        padding: 30px 0;
        gap: 0;
        height: calc(100vh - 80px);
        overflow-y: auto;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 10px 0;
        opacity: 0;
        transform: translateX(-50px);
        animation: slideInLeft 0.5s ease forwards;
    }

    .nav-menu.active .nav-item:nth-child(1) { animation-delay: 0.1s; }
    .nav-menu.active .nav-item:nth-child(2) { animation-delay: 0.2s; }
    .nav-menu.active .nav-item:nth-child(3) { animation-delay: 0.3s; }
    .nav-menu.active .nav-item:nth-child(4) { animation-delay: 0.4s; }

    @keyframes slideInLeft {
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .nav-link {
        padding: 20px 40px;
        font-size: 1.1rem;
        border-radius: 0;
        width: 100%;
        justify-content: center;
        border-bottom: 1px solid rgba(0, 255, 255, 0.1);
    }

    .nav-link:hover {
        background: rgba(0, 255, 255, 0.1);
        transform: none;
        box-shadow: none;
    }

    .nav-actions {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .nav-logo {
        font-size: 1.4rem;
    }

    .logo-icon {
        font-size: 1.6rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        height: 70px;
        padding: 0 15px;
    }

    .navbar.scrolled .nav-container {
        height: 60px;
    }

    .nav-menu {
        top: 70px;
        height: calc(100vh - 70px);
    }

    .navbar.scrolled .nav-menu {
        top: 60px;
        height: calc(100vh - 60px);
    }

    .nav-logo {
        font-size: 1.2rem;
    }

    .logo-icon {
        font-size: 1.4rem;
    }

    .nav-link {
        padding: 15px 30px;
        font-size: 1rem;
    }
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: radial-gradient(circle at center, #1a1a2e 0%, #0a0a0a 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%2300ffff" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
}

.hero-particles::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 10%;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 
        100px 200px var(--secondary-color),
        200px 100px var(--accent-color),
        300px 300px var(--primary-color),
        400px 150px var(--secondary-color),
        500px 250px var(--accent-color);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-content {
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.glitch {
    position: relative;
    color: var(--primary-color);
    text-shadow: 0 0 20px var(--primary-color);
    animation: glitch 2s infinite;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch::before {
    animation: glitch-1 0.5s infinite;
    color: var(--secondary-color);
    z-index: -1;
}

.glitch::after {
    animation: glitch-2 0.5s infinite;
    color: var(--accent-color);
    z-index: -2;
}

@keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(-2px, -2px); }
    20% { transform: translate(2px, 2px); }
    30% { transform: translate(-2px, 2px); }
    40% { transform: translate(2px, -2px); }
}

@keyframes glitch-2 {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(2px, 2px); }
    20% { transform: translate(-2px, -2px); }
    30% { transform: translate(2px, -2px); }
    40% { transform: translate(-2px, 2px); }
}

.arena-text {
    color: var(--secondary-color);
    text-shadow: 0 0 20px var(--secondary-color);
    margin-top: 10px;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    font-weight: 300;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 40px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-bg);
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.cta-button i {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

/* Gaming Hall Section */
.gaming-hall {
    padding: 100px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color);
    text-shadow: 0 0 20px var(--primary-color);
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 50px;
}

/* Status Legend */
.status-legend {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid transparent;
}

.status-indicator.available {
    background: var(--success-color);
    box-shadow: 0 0 10px var(--success-color);
}

.status-indicator.reserved {
    background: var(--danger-color);
    box-shadow: 0 0 10px var(--danger-color);
}

.status-indicator.in-use-soon {
    background: var(--warning-color);
    box-shadow: 0 0 10px var(--warning-color);
}

/* Enhanced 3D-Style Hall Layout */
.hall-layout {
    background: linear-gradient(135deg,
        rgba(26, 26, 46, 0.8) 0%,
        rgba(15, 15, 30, 0.9) 50%,
        rgba(26, 26, 46, 0.8) 100%
    );
    border: 3px solid transparent;
    border-image: linear-gradient(45deg,
        var(--primary-color),
        var(--secondary-color),
        var(--accent-color)
    ) 1;
    border-radius: 25px;
    padding: 0;
    margin-bottom: 40px;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 0 50px rgba(0, 255, 255, 0.2),
        inset 0 0 50px rgba(0, 255, 255, 0.05);
}

.hall-container {
    position: relative;
    padding: 30px;
    min-height: 600px;
}

/* Hall Environment */
.hall-environment {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.hall-walls {
    position: absolute;
    width: 100%;
    height: 100%;
}

.wall {
    position: absolute;
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.1),
        rgba(255, 0, 255, 0.1)
    );
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.wall-top {
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    border-radius: 2px 2px 0 0;
}

.wall-right {
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    border-radius: 0 2px 2px 0;
}

.wall-bottom {
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    border-radius: 0 0 2px 2px;
}

.wall-left {
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    border-radius: 2px 0 0 2px;
}

.hall-floor {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.floor-grid {
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: floorMove 30s linear infinite;
}

@keyframes floorMove {
    0% { background-position: 0 0, 0 0; }
    100% { background-position: 50px 50px, 50px 50px; }
}

.floor-lighting {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 70%, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 0, 255, 0.05) 0%, transparent 50%);
    animation: lightingPulse 8s ease-in-out infinite alternate;
}

@keyframes lightingPulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.6; }
}

.hall-ceiling {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 20px;
    background: linear-gradient(180deg,
        rgba(0, 255, 255, 0.2) 0%,
        transparent 100%
    );
}

.ceiling-lights {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 100%;
}

.ceiling-light {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 15px var(--primary-color);
    animation: ceilingGlow 3s ease-in-out infinite alternate;
}

@keyframes ceilingGlow {
    0% { opacity: 0.5; transform: scale(1); }
    100% { opacity: 1; transform: scale(1.2); }
}

/* Gaming Areas */
.gaming-areas {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-top: 30px;
}

.gaming-zone {
    background: rgba(255, 255, 255, 0.02);
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 25px;
    position: relative;
    transition: all 0.3s ease;
}

.gaming-zone:hover {
    border-color: rgba(0, 255, 255, 0.4);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.1);
}

.pc-zone {
    border-color: rgba(0, 255, 255, 0.3);
}

.ps5-zone {
    border-color: rgba(255, 0, 255, 0.3);
}

.zone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.zone-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.ps5-zone .zone-title {
    color: var(--secondary-color);
    text-shadow: 0 0 10px var(--secondary-color);
}

.zone-icon {
    font-size: 1.5rem;
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.zone-capacity {
    background: rgba(0, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.ps5-zone .zone-capacity {
    background: rgba(255, 0, 255, 0.1);
    border-color: rgba(255, 0, 255, 0.3);
}

.zone-layout {
    position: relative;
}

.zone-features {
    display: flex;
    gap: 20px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 255, 255, 0.05);
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    color: var(--text-secondary);
    border: 1px solid rgba(0, 255, 255, 0.1);
}

.feature-item i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.ps5-zone .feature-item {
    background: rgba(255, 0, 255, 0.05);
    border-color: rgba(255, 0, 255, 0.1);
}

.ps5-zone .feature-item i {
    color: var(--secondary-color);
}

/* Common Areas */
.common-areas {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 0, 0.2);
}

.lounge-area,
.snack-bar,
.restroom {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.lounge-area:hover,
.snack-bar:hover,
.restroom:hover {
    background: rgba(255, 255, 0, 0.1);
    transform: translateY(-2px);
}

.lounge-area i,
.snack-bar i,
.restroom i {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.lounge-area span,
.snack-bar span,
.restroom span {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Multi-Selection Panel */
.multi-selection-panel {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    max-height: 400px;
    background: linear-gradient(135deg,
        rgba(26, 26, 46, 0.95) 0%,
        rgba(15, 15, 30, 0.95) 100%
    );
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    backdrop-filter: blur(15px);
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1500;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.multi-selection-panel.active {
    transform: translateY(0);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.panel-header h4 {
    font-family: 'Orbitron', monospace;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
    margin: 0;
}

.clear-selection {
    background: rgba(255, 0, 64, 0.2);
    border: 1px solid rgba(255, 0, 64, 0.4);
    color: var(--danger-color);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.clear-selection:hover {
    background: rgba(255, 0, 64, 0.3);
    transform: scale(1.05);
}

.selected-stations {
    max-height: 200px;
    overflow-y: auto;
    padding: 15px;
}

.selected-station-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-bottom: 8px;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.selected-station-item:hover {
    background: rgba(0, 255, 255, 0.15);
    transform: translateX(5px);
}

.station-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.station-info i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.station-info span {
    font-weight: 500;
}

.remove-station {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.remove-station:hover {
    background: rgba(255, 0, 64, 0.2);
    transform: scale(1.1);
}

.panel-actions {
    padding: 20px;
    border-top: 1px solid rgba(0, 255, 255, 0.2);
}

.reserve-selected {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--dark-bg);
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.reserve-selected:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
}

.reserve-selected:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Multi-Reservation Modal */
.multi-reservation-content {
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
}

.selected-stations-summary {
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
}

.selected-stations-summary h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-family: 'Orbitron', monospace;
}

.stations-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.station-summary-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.05);
    padding: 10px;
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 255, 0.1);
}

.station-summary-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.station-summary-item.ps5-item i {
    color: var(--secondary-color);
}

.pricing-summary {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.pricing-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 255, 255, 0.1);
}

.pricing-item:last-child {
    border-bottom: none;
}

.pricing-total {
    display: flex;
    justify-content: space-between;
    padding: 15px 0 0;
    border-top: 2px solid var(--primary-color);
    margin-top: 15px;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary-color);
}

.field-hint {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 5px;
    opacity: 0.8;
}

.section-label {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    color: var(--secondary-color);
    text-align: center;
    margin-bottom: 30px;
    text-shadow: 0 0 10px var(--secondary-color);
}

.stations-grid {
    display: grid;
    gap: 15px;
    justify-items: center;
}

.pc-grid {
    grid-template-columns: repeat(5, 1fr);
}

.ps5-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 300px;
    margin: 0 auto;
}

/* Enhanced Gaming Station Styles */
.gaming-station {
    width: 90px;
    height: 90px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 3px solid transparent;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%
    );
    backdrop-filter: blur(10px);
    overflow: hidden;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 3D Effect Base */
.gaming-station::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 15px;
    background: linear-gradient(45deg, transparent, currentColor, transparent);
    opacity: 0;
    transition: all 0.4s ease;
    z-index: -1;
    animation: rotateRing 4s linear infinite;
    animation-play-state: paused;
}

/* Station Type Indicators */
.gaming-station.pc-station {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.1) 0%,
        rgba(0, 255, 255, 0.05) 100%
    );
}

.gaming-station.ps5-station {
    background: linear-gradient(135deg,
        rgba(255, 0, 255, 0.1) 0%,
        rgba(255, 0, 255, 0.05) 100%
    );
}

/* Multi-Selection Support */
.gaming-station.multi-selected {
    border-color: var(--accent-color);
    background: linear-gradient(135deg,
        rgba(255, 255, 0, 0.2) 0%,
        rgba(255, 255, 0, 0.1) 100%
    );
    box-shadow:
        0 0 25px rgba(255, 255, 0, 0.4),
        0 4px 20px rgba(0, 0, 0, 0.3);
    transform: scale(1.05);
}

.gaming-station.multi-selected::after {
    content: '✓';
    position: absolute;
    top: 5px;
    right: 5px;
    width: 18px;
    height: 18px;
    background: var(--accent-color);
    color: var(--dark-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    animation: checkmarkPop 0.3s ease;
}

@keyframes checkmarkPop {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes rotateRing {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Station Content */
.gaming-station i {
    font-size: 2.2rem;
    margin-bottom: 6px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 5px currentColor);
}

.station-number {
    font-size: 0.75rem;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 5px currentColor;
    background: rgba(0, 0, 0, 0.3);
    padding: 2px 6px;
    border-radius: 8px;
    border: 1px solid currentColor;
}

/* Enhanced Status Styles */
.gaming-station.available {
    border-color: var(--success-color);
    color: var(--success-color);
    box-shadow:
        0 0 20px rgba(0, 255, 0, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 0 20px rgba(0, 255, 0, 0.05);
}

.gaming-station.available:hover {
    transform: scale(1.1) translateY(-3px);
    box-shadow:
        0 0 30px rgba(0, 255, 0, 0.5),
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 0 25px rgba(0, 255, 0, 0.1);
}

.gaming-station.available:hover::before {
    opacity: 0.6;
    animation-play-state: running;
}

.gaming-station.available:hover i {
    transform: scale(1.1) rotate(5deg);
}

.gaming-station.reserved {
    border-color: var(--danger-color);
    color: var(--danger-color);
    box-shadow:
        0 0 20px rgba(255, 0, 64, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 0 20px rgba(255, 0, 64, 0.05);
    cursor: not-allowed;
    opacity: 0.8;
}

.gaming-station.reserved::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 25px;
    height: 25px;
    background: var(--danger-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: reservedPulse 2s infinite;
    opacity: 0.7;
}

.gaming-station.in-use-soon {
    border-color: var(--warning-color);
    color: var(--warning-color);
    box-shadow:
        0 0 20px rgba(255, 136, 0, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 0 20px rgba(255, 136, 0, 0.05);
    cursor: not-allowed;
}

.gaming-station.in-use-soon::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 10px;
    height: 10px;
    background: var(--warning-color);
    border-radius: 50%;
    animation: warningBlink 1s infinite;
    box-shadow: 0 0 8px var(--warning-color);
}

/* Single Selection State */
.gaming-station.selected {
    transform: scale(1.15) translateY(-5px);
    animation: selectedPulse 1.5s infinite;
    z-index: 10;
    border-color: var(--primary-color);
    box-shadow:
        0 0 35px rgba(0, 255, 255, 0.6),
        0 10px 30px rgba(0, 0, 0, 0.4);
}

.gaming-station.selected::before {
    opacity: 1;
    animation-play-state: running;
    background: linear-gradient(45deg,
        var(--primary-color),
        var(--secondary-color),
        var(--accent-color),
        var(--primary-color)
    );
}

@keyframes selectedPulse {
    0%, 100% {
        box-shadow:
            0 0 35px rgba(0, 255, 255, 0.6),
            0 10px 30px rgba(0, 0, 0, 0.4);
    }
    50% {
        box-shadow:
            0 0 50px rgba(0, 255, 255, 0.8),
            0 15px 40px rgba(0, 0, 0, 0.5);
    }
}

@keyframes reservedPulse {
    0%, 100% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes warningBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(26, 26, 46, 0.8);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid var(--primary-color);
    font-family: 'Orbitron', monospace;
}

.status-info {
    font-size: 1.1rem;
    font-weight: 600;
}

.last-updated {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border: 2px solid var(--primary-color);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    animation: modalSlideIn 0.3s ease;
    box-shadow: 0 0 50px rgba(0, 255, 255, 0.3);
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
}

.modal-header h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.close-modal {
    color: var(--text-secondary);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--danger-color);
    text-shadow: 0 0 10px var(--danger-color);
}

.modal-body {
    padding: 30px;
}

/* Selected Station Info */
.selected-station-info {
    margin-bottom: 30px;
}

.station-preview {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid var(--primary-color);
}

#selected-station-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    border-radius: 10px;
    border: 2px solid var(--success-color);
    color: var(--success-color);
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
}

.station-details h4 {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.station-details p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Form Styles */
.reservation-form {
    display: grid;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-size: 1rem;
}

.form-group input {
    padding: 12px 15px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.error-message {
    color: var(--danger-color);
    font-size: 0.9rem;
    margin-top: 5px;
    display: none;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn-cancel,
.btn-reserve {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.btn-reserve {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: var(--dark-bg);
    font-weight: 700;
}

.btn-reserve:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 255, 255, 0.4);
}

/* Confirmation Modal */
.confirmation-content {
    text-align: center;
    max-width: 500px;
}

.confirmation-header {
    padding: 40px 30px 20px;
}

.success-icon {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 20px;
    animation: successPulse 1s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.confirmation-header h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    color: var(--success-color);
    text-shadow: 0 0 10px var(--success-color);
}

.confirmation-body {
    padding: 20px 30px 40px;
}

#confirmation-details {
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid var(--success-color);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: left;
}

.btn-close-confirmation {
    background: var(--success-color);
    color: var(--dark-bg);
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.btn-close-confirmation:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 255, 0, 0.4);
}

/* Additional Animations and Effects */
.gaming-station::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, currentColor, transparent);
    border-radius: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.gaming-station:hover::before {
    opacity: 0.3;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.gaming-station.reserved::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--danger-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: reservedPulse 2s infinite;
}

@keyframes reservedPulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.gaming-station.in-use-soon::after {
    content: '';
    position: absolute;
    top: 5px;
    right: 5px;
    width: 8px;
    height: 8px;
    background: var(--warning-color);
    border-radius: 50%;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Countdown Timer Animation */
.countdown-timer {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background: var(--warning-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    color: var(--dark-bg);
    animation: countdownPulse 1s infinite;
}

@keyframes countdownPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Particle Effects */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 3s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Hover Effects for Navigation */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

/* Enhanced Button Animations */
.cta-button {
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.cta-button:hover::before {
    left: 100%;
}

/* Form Input Focus Effects */
.form-group input:focus {
    animation: inputGlow 0.3s ease;
}

@keyframes inputGlow {
    0% { box-shadow: 0 0 5px rgba(0, 255, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.6); }
    100% { box-shadow: 0 0 10px rgba(0, 255, 255, 0.3); }
}

/* Status Bar Animation */
.status-bar {
    position: relative;
    overflow: hidden;
}

.status-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    animation: statusBarScan 3s linear infinite;
}

@keyframes statusBarScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 20px;
    }

    .nav-menu {
        display: none;
    }

    .hall-grid {
        grid-template-columns: 1fr;
    }

    .pc-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .status-legend {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .pc-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .gaming-station {
        width: 60px;
        height: 60px;
    }

    .gaming-station i {
        font-size: 1.5rem;
    }

    .station-number {
        font-size: 0.7rem;
    }
}
